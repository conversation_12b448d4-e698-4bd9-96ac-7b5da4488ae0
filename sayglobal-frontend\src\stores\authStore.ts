import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { AuthUser } from '@/types';

interface AuthState {
    user: AuthUser | null;
    isLoading: boolean;
    isAuthenticated: boolean;
    error: string | null;
}

// Artık store'un kendi içinde action'ları yok.
// Sadece state ve basit setter'lar var.
interface AuthActions {
    clearError: () => void;
    setLoading: (loading: boolean) => void;
}

type AuthStore = AuthState & AuthActions;


export const useAuthStore = create<AuthStore>()(
    devtools(
        (set) => ({
            // Initial state
            user: null,
            isLoading: false,
            isAuthenticated: false,
            error: null,

            // Actions
            // 🗑️ login fonksiyonu tamamen silindi. Artık useLoginMutation kullanılıyor.

            // 🗑️ logout fonksiyonu tamamen silindi. Artık useLogoutMutation kullanılıyor.

            clearError: () => set({ error: null }),

            setLoading: (loading: boolean) => set({ isLoading: loading }),
        }),
        {
            name: 'auth-store',
        }
    )
);

// 🗑️ Window event listener'ları silindi. Bu mantık artık AuthContext ve interceptor'da. 