import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
    ProductFormData,
    ProductRatios,
    ProductPoints,
    SubCategoryFeature,
    ProductImage,
    VariantFormData,
} from '@/types';

// State Interface
interface AddProductState {
    formData: ProductFormData;
    selectedNames: {
        brandName: string;
        categoryName: string;
        subCategoryName: string;
    };
    selectedFeatureDetails: { featureName: string; featureValue: string }[];
    variants: VariantFormData[];
    availableFeatures: SubCategoryFeature[];
    error: string | null;

    // Actions
    setFormData: (data: Partial<ProductFormData>) => void;
    handleInputChange: (field: keyof ProductFormData, value: unknown) => void;
    handleRatioChange: (field: keyof ProductRatios, value: number) => void;
    setCategorySelection: (data: {
        brandId: number;
        categoryId: number;
        subCategoryId: number;
        selectedFeatures: { [key: number]: number[] };
        brandName: string;
        categoryName: string;
        subCategoryName: string;
        selectedFeatureDetails: { featureName: string; featureValue: string }[];
    }) => void;
    clearAllSelections: () => void;
    setAvailableFeatures: (features: SubCategoryFeature[]) => void;

    // Variant Actions
    setVariants: (variants: VariantFormData[]) => void;
    saveVariant: (variant: VariantFormData, editingVariantId?: number) => void;
    deleteVariant: (variantId: number) => void;
    generateVariants: (generatedVariants: VariantFormData[]) => void;

    // Image Actions
    handleImageUpload: (files: FileList) => void;
    removeImage: (index: number) => void;
    setMainImage: (index: number) => void;

    // Utility Actions
    setError: (error: string | null) => void;
    reset: () => void;
}

// Initial State
const initialState: Omit<AddProductState, keyof AddProductActions> = {
    formData: {
        name: '',
        description: '',
        brandId: 0,
        categoryId: 0,
        subCategoryId: 0,
        selectedFeatures: {},
        price: 0,
        stock: 0,
        extraDiscount: 0,
        ratios: { pvRatio: 0, cvRatio: 0, spRatio: 0 },
        points: { pv: 0, cv: 0, sp: 0 },
        hasVariants: false,
        variants: [],
        images: [],
        isActive: true,
    },
    selectedNames: {
        brandName: '',
        categoryName: '',
        subCategoryName: '',
    },
    selectedFeatureDetails: [],
    variants: [],
    availableFeatures: [],
    error: null,
};

// Actions mapped to interface
interface AddProductActions {
    setFormData: (data: Partial<ProductFormData>) => void;
    handleInputChange: (field: keyof ProductFormData, value: unknown) => void;
    handleRatioChange: (field: keyof ProductRatios, value: number) => void;
    setCategorySelection: (data: { brandId: number; categoryId: number; subCategoryId: number }) => void;
    clearAllSelections: () => void;
    setAvailableFeatures: (features: SubCategoryFeature[]) => void;
    setVariants: (variants: VariantFormData[]) => void;
    saveVariant: (variant: VariantFormData, editingVariantId?: number) => void;
    deleteVariant: (variantId: number) => void;
    generateVariants: (generatedVariants: VariantFormData[]) => void;
    handleImageUpload: (files: FileList) => void;
    removeImage: (index: number) => void;
    setMainImage: (index: number) => void;
    setError: (error: string | null) => void;
    reset: () => void;
}


// Utility Functions
const calculatePoints = (price: number, ratios: ProductRatios): ProductPoints => {
    return {
        pv: Math.round(price * (ratios.pvRatio / 100)),
        cv: Math.round(price * (ratios.cvRatio / 100)),
        sp: Math.round(price * (ratios.spRatio / 100)),
    };
};

const recalculateFeaturesFromVariants = (variants: VariantFormData[]) => {
    const newSelectedFeatures: { [key: number]: number[] } = {};
    const newSelectedFeatureDetails: { featureName: string; featureValue: string }[] = [];

    variants.forEach(variant => {
        variant.featureDetails.forEach(detail => {
            if (!newSelectedFeatures[detail.featureDefinitionId]) {
                newSelectedFeatures[detail.featureDefinitionId] = [];
            }
            if (!newSelectedFeatures[detail.featureDefinitionId].includes(detail.featureValueId)) {
                newSelectedFeatures[detail.featureDefinitionId].push(detail.featureValueId);
            }

            const exists = newSelectedFeatureDetails.some(
                existing => existing.featureName === detail.featureName && existing.featureValue === detail.featureValue
            );
            if (!exists) {
                newSelectedFeatureDetails.push({
                    featureName: detail.featureName,
                    featureValue: detail.featureValue,
                });
            }
        });
    });

    return { newSelectedFeatures, newSelectedFeatureDetails };
};

export const useAddProductStore = create<AddProductState>()(
    devtools(
        (set, get) => ({
            ...initialState,

            setFormData: (data) => set(state => ({ formData: { ...state.formData, ...data } })),

            handleInputChange: (field, value) => {
                const oldFormData = get().formData;
                let newFormData = { ...oldFormData, [field]: value };

                if (field === 'price' || field === 'ratios') {
                    if (!newFormData.hasVariants) {
                        const points = calculatePoints(newFormData.price, newFormData.ratios);
                        newFormData = { ...newFormData, points };
                    }
                }

                set({ formData: newFormData });
            },

            handleRatioChange: (field, value) => {
                const oldFormData = get().formData;
                const newRatios = { ...oldFormData.ratios, [field]: value };
                let newFormData = { ...oldFormData, ratios: newRatios };

                if (!newFormData.hasVariants) {
                    const points = calculatePoints(newFormData.price, newRatios);
                    newFormData = { ...newFormData, points };
                }

                set({ formData: newFormData });
            },

            setCategorySelection: (data) => {
                set({
                    formData: {
                        ...get().formData,
                        brandId: data.brandId,
                        categoryId: data.categoryId,
                        subCategoryId: data.subCategoryId,
                        selectedFeatures: data.selectedFeatures,
                        hasVariants: false, // Reset variants
                    },
                    selectedNames: {
                        brandName: data.brandName,
                        categoryName: data.categoryName,
                        subCategoryName: data.subCategoryName,
                    },
                    selectedFeatureDetails: data.selectedFeatureDetails,
                    error: null,
                });
            },

            clearAllSelections: () => {
                set({
                    formData: {
                        ...get().formData,
                        brandId: 0,
                        categoryId: 0,
                        subCategoryId: 0,
                        selectedFeatures: {},
                        hasVariants: false,
                    },
                    selectedNames: { brandName: '', categoryName: '', subCategoryName: '' },
                    selectedFeatureDetails: [],
                    availableFeatures: [],
                    variants: [],
                    error: null,
                });
            },

            setAvailableFeatures: (features) => set({ availableFeatures: features }),

            setVariants: (variants) => set({ variants: variants }),

            saveVariant: (variant, editingVariantId) => {
                const { variants } = get();
                let updatedVariants: VariantFormData[];

                if (editingVariantId) {
                    updatedVariants = variants.map(v => (v.id === editingVariantId ? variant : v));
                } else {
                    updatedVariants = [...variants, { ...variant, id: Date.now() }];
                }

                const { newSelectedFeatures, newSelectedFeatureDetails } = recalculateFeaturesFromVariants(updatedVariants);

                set(state => ({
                    variants: updatedVariants,
                    formData: { ...state.formData, selectedFeatures: newSelectedFeatures, hasVariants: updatedVariants.length > 1 },
                    selectedFeatureDetails: newSelectedFeatureDetails,
                }));
            },

            deleteVariant: (variantId) => {
                const { variants } = get();
                const newVariants = variants.filter(v => v.id !== variantId);
                const { newSelectedFeatures, newSelectedFeatureDetails } = recalculateFeaturesFromVariants(newVariants);

                set(state => ({
                    variants: newVariants,
                    formData: { ...state.formData, selectedFeatures: newSelectedFeatures, hasVariants: newVariants.length > 1 },
                    selectedFeatureDetails: newSelectedFeatureDetails,
                }));
            },

            generateVariants: (generatedVariants) => {
                const { newSelectedFeatures, newSelectedFeatureDetails } = recalculateFeaturesFromVariants(generatedVariants);
                set(state => ({
                    formData: { ...state.formData, hasVariants: true, selectedFeatures: newSelectedFeatures },
                    selectedFeatureDetails: newSelectedFeatureDetails,
                    variants: generatedVariants,
                    error: null
                }));
            },

            handleImageUpload: (files) => {
                const { formData } = get();
                const newImages: ProductImage[] = Array.from(files).map((file, index) => ({
                    url: URL.createObjectURL(file),
                    isMain: formData.images.length === 0 && index === 0,
                    sortOrder: formData.images.length + index,
                    file: file,
                }));

                set({
                    formData: { ...formData, images: [...formData.images, ...newImages] }
                });
            },

            removeImage: (index) => {
                const { formData } = get();
                const newImages = formData.images.filter((_, i) => i !== index);
                if (formData.images[index].isMain && newImages.length > 0) {
                    newImages[0].isMain = true;
                }
                set({
                    formData: { ...formData, images: newImages }
                });
            },

            setMainImage: (index) => {
                const { formData } = get();
                const newImages = formData.images.map((img, i) => ({ ...img, isMain: i === index }));
                set({
                    formData: { ...formData, images: newImages }
                });
            },

            setError: (error) => set({ error }),
            reset: () => set({ ...initialState }),
        }),
        {
            name: 'add-product-store',
            enabled: process.env.NODE_ENV === 'development',
        }
    )
);

// Selector hooks for performance
export const useProductFormData = () => useAddProductStore(state => state.formData);
export const useProductVariants = () => useAddProductStore(state => state.variants);
export const useProductActions = () => useAddProductStore(state => ({
    setFormData: state.setFormData,
    handleInputChange: state.handleInputChange,
    handleRatioChange: state.handleRatioChange,
    setCategorySelection: state.setCategorySelection,
    clearAllSelections: state.clearAllSelections,
    setAvailableFeatures: state.setAvailableFeatures,
    setVariants: state.setVariants,
    saveVariant: state.saveVariant,
    deleteVariant: state.deleteVariant,
    generateVariants: state.generateVariants,
    handleImageUpload: state.handleImageUpload,
    removeImage: state.removeImage,
    setMainImage: state.setMainImage,
    setError: state.setError,
    reset: state.reset
})); 