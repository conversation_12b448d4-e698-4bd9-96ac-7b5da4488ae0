import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import apiClient from '@/services/api';
import { API_ENDPOINTS } from '@/constants/apiEndpoints';

interface Brand {
    id: number;
    name: string;
    logoUrl?: string;
}

interface Category {
    id: number;
    name: string;
}

interface SubCategory {
    id: number;
    name: string;
    categoryId: number;
}

interface FeatureDefinition {
    id: number;
    name: string;
    description?: string;
    isRequired?: boolean;
    isMultiSelect?: boolean;
}

interface FeatureValue {
    id: number;
    featureDefinitionId: number;
    value: string;
}

interface BrandCategory {
    id: number;
    brandId: number;
    categoryId: number;
}

interface SubCategoryFeature {
    id: number;
    subCategoryId: number;
    featureDefinitionId: number;
}

interface CategoryManagementState {
    // Data
    brands: Brand[];
    categories: Category[];
    subCategories: SubCategory[];
    featureDefinitions: FeatureDefinition[];
    featureValues: FeatureValue[];
    brandCategories: BrandCategory[];
    subCategoryFeatures: SubCategoryFeature[];

    // Loading states
    isLoading: boolean;
    isCreating: boolean;
    isFetching: boolean;

    // Actions
    setBrands: (brands: Brand[]) => void;
    setCategories: (categories: Category[]) => void;
    setSubCategories: (subCategories: SubCategory[]) => void;
    setFeatureDefinitions: (featureDefinitions: FeatureDefinition[]) => void;
    setFeatureValues: (featureValues: FeatureValue[]) => void;
    setBrandCategories: (brandCategories: BrandCategory[]) => void;
    setSubCategoryFeatures: (subCategoryFeatures: SubCategoryFeature[]) => void;

    // API calls
    fetchBrands: () => Promise<void>;
    fetchCategories: () => Promise<void>;
    fetchSubCategories: (categoryId?: number) => Promise<void>;
    fetchFeatureDefinitions: () => Promise<void>;
    fetchFeatureValues: (definitionId?: number) => Promise<void>;
    fetchBrandCategories: (brandId?: number) => Promise<void>;
    fetchSubCategoryFeatures: (subCategoryId?: number) => Promise<void>;

    createBrand: (brandData: { name: string; logoUrl?: string }) => Promise<void>;
    createCategory: (categoryData: { name: string }) => Promise<void>;
    createSubCategory: (subCategoryData: { name: string; categoryId: number }) => Promise<void>;
    createFeatureDefinition: (definitionData: { name: string; description?: string; isRequired?: boolean; isMultiSelect?: boolean }) => Promise<void>;
    createFeatureValue: (valueData: { featureDefinitionId: number; value: string }) => Promise<void>;
    createBrandCategory: (brandCategoryData: { brandId: number; categoryId: number }) => Promise<void>;
    createSubCategoryFeature: (subCategoryFeatureData: { categoryId: number; featureDefinitionId: number }) => Promise<void>;

    // Utility
    setLoading: (loading: boolean) => void;
    setCreating: (creating: boolean) => void;
    setFetching: (fetching: boolean) => void;
}

export const useCategoryManagementStore = create<CategoryManagementState>()(
    devtools(
        (set) => ({
            // Initial state
            brands: [],
            categories: [],
            subCategories: [],
            featureDefinitions: [],
            featureValues: [],
            brandCategories: [],
            subCategoryFeatures: [],
            isLoading: false,
            isCreating: false,
            isFetching: false,

            // Setters
            setBrands: (brands) => set({ brands }),
            setCategories: (categories) => set({ categories }),
            setSubCategories: (subCategories) => set({ subCategories }),
            setFeatureDefinitions: (featureDefinitions) => set({ featureDefinitions }),
            setFeatureValues: (featureValues) => set({ featureValues }),
            setBrandCategories: (brandCategories) => set({ brandCategories }),
            setSubCategoryFeatures: (subCategoryFeatures) => set({ subCategoryFeatures }),

            setLoading: (loading) => set({ isLoading: loading }),
            setCreating: (creating) => set({ isCreating: creating }),
            setFetching: (fetching) => set({ isFetching: fetching }),

            // API calls
            fetchBrands: async () => {
                set({ isFetching: true });
                try {
                    const response = await apiClient.get(API_ENDPOINTS.GET_BRANDS);
                    set({ brands: response.data });
                } catch (error) {
                    console.error('Markalar yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            fetchCategories: async () => {
                set({ isFetching: true });
                try {
                    const response = await apiClient.get(API_ENDPOINTS.GET_CATEGORIES);
                    set({ categories: response.data });
                } catch (error) {
                    console.error('Kategoriler yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            fetchSubCategories: async (categoryId?: number) => {
                set({ isFetching: true });
                try {
                    const url = categoryId
                        ? API_ENDPOINTS.GET_SUBCATEGORIES_BY_CATEGORY.replace('{categoryId}', categoryId.toString())
                        : '/api/Products/subcategories';
                    const response = await apiClient.get(url);
                    set({ subCategories: response.data });
                } catch (error) {
                    console.error('Alt kategoriler yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            fetchFeatureDefinitions: async () => {
                set({ isFetching: true });
                try {
                    const response = await apiClient.get(API_ENDPOINTS.GET_SUBCATEGORY_FEATURES);
                    set({ featureDefinitions: response.data });
                } catch (error) {
                    console.error('Özellik tanımları yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            fetchFeatureValues: async (definitionId?: number) => {
                set({ isFetching: true });
                try {
                    const url = definitionId
                        ? API_ENDPOINTS.GET_FEATURE_VALUES_BY_DEFINITION_ID.replace('{definitionId}', definitionId.toString())
                        : API_ENDPOINTS.GET_FEATURE_VALUES;
                    const response = await apiClient.get(url);
                    set({ featureValues: response.data });
                } catch (error) {
                    console.error('Özellik değerleri yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            fetchBrandCategories: async (brandId?: number) => {
                set({ isFetching: true });
                try {
                    if (!brandId) {
                        set({ brandCategories: [] });
                        return;
                    }
                    const url = API_ENDPOINTS.GET_CATEGORIES_BY_BRAND_ID.replace('{brandId}', brandId.toString());
                    const response = await apiClient.get(url);
                    const categoriesAsBrandCategories = response.data.map((cat: { id: number;[key: string]: unknown }) => ({ ...cat, brandId: brandId, categoryId: cat.id }));
                    set({ brandCategories: categoriesAsBrandCategories });
                } catch (error) {
                    console.error('Marka-kategori ilişkileri yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            fetchSubCategoryFeatures: async (subCategoryId?: number) => {
                set({ isFetching: true });
                try {
                    const url = subCategoryId
                        ? API_ENDPOINTS.GET_SUBCATEGORY_FEATURES_BY_ID.replace('{subCategoryId}', subCategoryId.toString())
                        : API_ENDPOINTS.GET_SUBCATEGORY_FEATURES;
                    const response = await apiClient.get(url);
                    set({ subCategoryFeatures: response.data });
                } catch (error) {
                    console.error('Alt kategori-özellik ilişkileri yüklenirken hata:', error);
                } finally {
                    set({ isFetching: false });
                }
            },

            // Create functions
            createBrand: async (brandData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_BRAND, brandData);
                    const newBrand = response.data;
                    set(state => ({ brands: [...state.brands, newBrand] }));
                } catch (error) {
                    console.error('Marka oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },

            createCategory: async (categoryData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_CATEGORY, categoryData);
                    const newCategory = response.data;
                    set(state => ({ categories: [...state.categories, newCategory] }));
                } catch (error) {
                    console.error('Kategori oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },

            createSubCategory: async (subCategoryData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_SUBCATEGORY, subCategoryData);
                    const newSubCategory = response.data;
                    set(state => ({ subCategories: [...state.subCategories, newSubCategory] }));
                } catch (error) {
                    console.error('Alt kategori oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },

            createFeatureDefinition: async (definitionData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_FEATURE_DEFINITION, definitionData);
                    const newDefinition = response.data;
                    set(state => ({ featureDefinitions: [...state.featureDefinitions, newDefinition] }));
                } catch (error) {
                    console.error('Özellik tanımı oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },

            createFeatureValue: async (valueData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_FEATURE_VALUE, valueData);
                    const newValue = response.data;
                    set(state => ({ featureValues: [...state.featureValues, newValue] }));
                } catch (error) {
                    console.error('Özellik değeri oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },

            createBrandCategory: async (brandCategoryData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_BRAND_CATEGORY, brandCategoryData);
                    const newBrandCategory = response.data;
                    set(state => ({ brandCategories: [...state.brandCategories, newBrandCategory] }));
                } catch (error) {
                    console.error('Marka-kategori ilişkisi oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },

            createSubCategoryFeature: async (subCategoryFeatureData) => {
                set({ isCreating: true });
                try {
                    const response = await apiClient.post(API_ENDPOINTS.CREATE_SUBCATEGORY_FEATURE, subCategoryFeatureData);
                    const newSubCategoryFeature = response.data;
                    set(state => ({ subCategoryFeatures: [...state.subCategoryFeatures, newSubCategoryFeature] }));
                } catch (error) {
                    console.error('Alt kategori-özellik ilişkisi oluşturulurken hata:', error);
                    throw error;
                } finally {
                    set({ isCreating: false });
                }
            },
        }),
        {
            name: 'category-management-store',
        }
    )
); 