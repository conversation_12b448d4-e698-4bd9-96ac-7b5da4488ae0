import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import {
    ProductFormData,
    ProductRatios,
    ProductPoints,
    SubCategoryFeature,
    ProductImage,
    VariantFormData,
} from '@/types';

// API'den gelen product data için interface
interface ApiProductData {
    variants?: Array<{
        id: number;
        price?: number;
        pv?: number;
        cv?: number;
        sp?: number;
        features?: Array<{
            featureDefinitionId: number;
            featureValueId: number;
            featureName?: string;
            featureValue?: string;
        }>;
        images?: Array<{
            id: number;
            url: string;
            isMain: boolean;
            sortOrder: number;
        }>;
    }>;
    [key: string]: unknown;
}

// State Interface
interface EditProductState {
    formData: ProductFormData;
    selectedNames: {
        brandName: string;
        categoryName: string;
        subCategoryName: string;
    };
    selectedFeatureDetails: { featureName: string; featureValue: string }[];
    variants: VariantFormData[];
    availableFeatures: SubCategoryFeature[];
    error: string | null;
    originalProductId: number | null;

    // Actions
    setFormData: (data: Partial<ProductFormData>) => void;
    handleInputChange: (field: keyof ProductFormData, value: unknown) => void;
    handleRatioChange: (field: keyof ProductRatios, value: number) => void;
    setCategorySelection: (data: {
        brandId: number;
        categoryId: number;
        subCategoryId: number;
        selectedFeatures: { [key: number]: number[] };
        brandName: string;
        categoryName: string;
        subCategoryName: string;
        selectedFeatureDetails: { featureName: string; featureValue: string }[];
    }) => void;
    clearAllSelections: () => void;
    setAvailableFeatures: (features: SubCategoryFeature[]) => void;

    // Variant Actions
    setVariants: (variants: VariantFormData[]) => void;
    saveVariant: (variant: VariantFormData, editingVariantId?: number) => void;
    deleteVariant: (variantId: number) => void;
    generateVariants: (generatedVariants: VariantFormData[]) => void;

    // Image Actions
    handleImageUpload: (files: FileList) => void;
    removeImage: (index: number) => void;
    setMainImage: (index: number) => void;

    // Utility Actions
    setError: (error: string | null) => void;
    reset: () => void;

    // Edit specific actions
    initializeWithProduct: (productData: ApiProductData) => void;
    setOriginalProductId: (id: number) => void;
}

// Initial State
const initialState: Omit<EditProductState, keyof EditProductActions> = {
    formData: {
        name: '',
        description: '',
        brandId: 0,
        categoryId: 0,
        subCategoryId: 0,
        selectedFeatures: {},
        price: 0,
        stock: 0,
        extraDiscount: 0,
        ratios: { pvRatio: 0, cvRatio: 0, spRatio: 0 },
        points: { pv: 0, cv: 0, sp: 0 },
        hasVariants: false,
        variants: [],
        images: [],
        isActive: true,
    },
    selectedNames: {
        brandName: '',
        categoryName: '',
        subCategoryName: '',
    },
    selectedFeatureDetails: [],
    variants: [],
    availableFeatures: [],
    error: null,
    originalProductId: null,
};

// Actions mapped to interface
interface EditProductActions {
    setFormData: (data: Partial<ProductFormData>) => void;
    handleInputChange: (field: keyof ProductFormData, value: unknown) => void;
    handleRatioChange: (field: keyof ProductRatios, value: number) => void;
    setCategorySelection: (data: { brandId: number; categoryId: number; subCategoryId: number }) => void;
    clearAllSelections: () => void;
    setAvailableFeatures: (features: SubCategoryFeature[]) => void;
    setVariants: (variants: VariantFormData[]) => void;
    saveVariant: (variant: VariantFormData, editingVariantId?: number) => void;
    deleteVariant: (variantId: number) => void;
    generateVariants: (generatedVariants: VariantFormData[]) => void;
    handleImageUpload: (files: FileList) => void;
    removeImage: (index: number) => void;
    setMainImage: (index: number) => void;
    setError: (error: string | null) => void;
    reset: () => void;
    initializeWithProduct: (productData: ApiProductData) => void;
    setOriginalProductId: (id: number) => void;
}

// Utility Functions
const calculatePoints = (price: number, ratios: ProductRatios): ProductPoints => {
    return {
        pv: Math.round(price * (ratios.pvRatio / 100)),
        cv: Math.round(price * (ratios.cvRatio / 100)),
        sp: Math.round(price * (ratios.spRatio / 100)),
    };
};

const recalculateFeaturesFromVariants = (variants: VariantFormData[]) => {
    const newSelectedFeatures: { [key: number]: number[] } = {};
    const newSelectedFeatureDetails: { featureName: string; featureValue: string }[] = [];

    variants.forEach(variant => {
        variant.featureDetails.forEach(detail => {
            if (!newSelectedFeatures[detail.featureDefinitionId]) {
                newSelectedFeatures[detail.featureDefinitionId] = [];
            }
            if (!newSelectedFeatures[detail.featureDefinitionId].includes(detail.featureValueId)) {
                newSelectedFeatures[detail.featureDefinitionId].push(detail.featureValueId);
            }

            const exists = newSelectedFeatureDetails.some(
                existing => existing.featureName === detail.featureName && existing.featureValue === detail.featureValue
            );
            if (!exists) {
                newSelectedFeatureDetails.push({
                    featureName: detail.featureName,
                    featureValue: detail.featureValue,
                });
            }
        });
    });

    return { newSelectedFeatures, newSelectedFeatureDetails };
};

export const useEditProductStore = create<EditProductState>()(
    devtools(
        (set, get) => ({
            ...initialState,

            setFormData: (data) => set(state => ({ formData: { ...state.formData, ...data } })),

            handleInputChange: (field, value) => {
                const oldFormData = get().formData;
                let newFormData = { ...oldFormData, [field]: value };

                if (field === 'price' || field === 'ratios') {
                    if (!newFormData.hasVariants) {
                        const points = calculatePoints(newFormData.price, newFormData.ratios);
                        newFormData = { ...newFormData, points };
                    }
                }

                set({ formData: newFormData });
            },

            handleRatioChange: (field, value) => {
                const oldFormData = get().formData;
                const newRatios = { ...oldFormData.ratios, [field]: value };
                let newFormData = { ...oldFormData, ratios: newRatios };

                if (!newFormData.hasVariants) {
                    const points = calculatePoints(newFormData.price, newRatios);
                    newFormData = { ...newFormData, points };
                }

                set({ formData: newFormData });
            },

            setCategorySelection: (data) => {
                set({
                    formData: {
                        ...get().formData,
                        brandId: data.brandId,
                        categoryId: data.categoryId,
                        subCategoryId: data.subCategoryId,
                        selectedFeatures: data.selectedFeatures,
                        hasVariants: false, // Reset variants
                    },
                    selectedNames: {
                        brandName: data.brandName,
                        categoryName: data.categoryName,
                        subCategoryName: data.subCategoryName,
                    },
                    selectedFeatureDetails: data.selectedFeatureDetails,
                    error: null,
                });
            },

            clearAllSelections: () => {
                set({
                    formData: {
                        ...get().formData,
                        brandId: 0,
                        categoryId: 0,
                        subCategoryId: 0,
                        selectedFeatures: {},
                        hasVariants: false,
                    },
                    selectedNames: { brandName: '', categoryName: '', subCategoryName: '' },
                    selectedFeatureDetails: [],
                    availableFeatures: [],
                    variants: [],
                    error: null,
                });
            },

            setAvailableFeatures: (features) => set({ availableFeatures: features }),

            setVariants: (variants) => set({ variants: variants }),

            saveVariant: (variant, editingVariantId) => {
                const { variants } = get();
                let updatedVariants: VariantFormData[];

                if (editingVariantId) {
                    updatedVariants = variants.map(v => (v.id === editingVariantId ? variant : v));
                } else {
                    updatedVariants = [...variants, { ...variant, id: Date.now() }];
                }

                const { newSelectedFeatures, newSelectedFeatureDetails } = recalculateFeaturesFromVariants(updatedVariants);

                set(state => ({
                    variants: updatedVariants,
                    formData: { ...state.formData, selectedFeatures: newSelectedFeatures, hasVariants: updatedVariants.length > 1 },
                    selectedFeatureDetails: newSelectedFeatureDetails,
                }));
            },

            deleteVariant: (variantId) => {
                const { variants } = get();
                const newVariants = variants.filter(v => v.id !== variantId);
                const { newSelectedFeatures, newSelectedFeatureDetails } = recalculateFeaturesFromVariants(newVariants);

                set(state => ({
                    variants: newVariants,
                    formData: { ...state.formData, selectedFeatures: newSelectedFeatures, hasVariants: newVariants.length > 1 },
                    selectedFeatureDetails: newSelectedFeatureDetails,
                }));
            },

            generateVariants: (generatedVariants) => {
                const { newSelectedFeatures, newSelectedFeatureDetails } = recalculateFeaturesFromVariants(generatedVariants);
                set(state => ({
                    formData: { ...state.formData, hasVariants: true, selectedFeatures: newSelectedFeatures },
                    selectedFeatureDetails: newSelectedFeatureDetails,
                    variants: generatedVariants,
                    error: null
                }));
            },

            handleImageUpload: (files) => {
                const { formData } = get();
                const newImages: ProductImage[] = Array.from(files).map((file, index) => ({
                    url: URL.createObjectURL(file),
                    isMain: formData.images.length === 0 && index === 0,
                    sortOrder: formData.images.length + index,
                    file: file,
                }));

                set({
                    formData: { ...formData, images: [...formData.images, ...newImages] }
                });
            },

            removeImage: (index) => {
                const { formData } = get();
                const newImages = formData.images.filter((_, i) => i !== index);
                if (formData.images[index].isMain && newImages.length > 0) {
                    newImages[0].isMain = true;
                }
                set({
                    formData: { ...formData, images: newImages }
                });
            },

            setMainImage: (index) => {
                const { formData } = get();
                const newImages = formData.images.map((img, i) => ({ ...img, isMain: i === index }));
                set({
                    formData: { ...formData, images: newImages }
                });
            },

            setError: (error) => set({ error }),
            reset: () => set({ ...initialState }),

            // Edit specific methods
            initializeWithProduct: (productData) => {
                console.log('🔄 Initializing product data:', productData);
                console.log('🖼️ Variant images from API:', productData.variants?.map((v) => ({
                    variantId: v.id,
                    hasImages: !!v.images,
                    imagesCount: v.images?.length || 0,
                    images: v.images
                })));

                // Varyantları dönüştür
                const convertedVariants: VariantFormData[] = productData.variants?.map((variant, index: number) => {
                    // Variant features'ları dönüştür - backend'den doğru field'ları kullan
                    const featureDetails = variant.features?.map((feature) => ({
                        featureDefinitionId: feature.featureDefinitionId, // API'den gelen doğru field
                        featureValueId: feature.featureValueId, // API'den gelen doğru field
                        featureName: feature.featureName || '',
                        featureValue: feature.featureValue || '',
                    })) || [];

                    // Selected features'ı oluştur (geçici, kategori seçiminde güncellenecek)
                    const selectedFeatures: { [key: number]: number[] } = {};
                    featureDetails.forEach((detail) => {
                        if (!selectedFeatures[detail.featureDefinitionId]) {
                            selectedFeatures[detail.featureDefinitionId] = [];
                        }
                        if (!selectedFeatures[detail.featureDefinitionId].includes(detail.featureValueId)) {
                            selectedFeatures[detail.featureDefinitionId].push(detail.featureValueId);
                        }
                    });

                    // Variant name'i features'lardan oluştur
                    const variantName = featureDetails.length > 0
                        ? featureDetails.map((f) => f.featureValue).join(' - ')
                        : `Varyant ${index + 1}`;

                    const price = variant.price || 0;
                    // API'den gelen pv, cv, sp değerleri zaten oran olarak geliyor
                    const ratios = {
                        pvRatio: variant.pv || 0,
                        cvRatio: variant.cv || 0,
                        spRatio: variant.sp || 0,
                    };

                    return {
                        id: variant.id || Date.now() + index,
                        name: variantName,
                        pricing: {
                            price: price,
                            stock: variant.stock || 0,
                            extraDiscount: variant.extraDiscount || 0,
                            ratios: ratios,
                            points: calculatePoints(price, ratios)
                        },
                        selectedFeatures: selectedFeatures,
                        features: featureDetails, // API'den gelen feature bilgileri
                        featureDetails: featureDetails,
                        images: variant.images?.map((img) => ({
                            id: img.id,
                            url: img.url,
                            isMain: img.isMain,
                            sortOrder: img.sortOrder,
                            file: null,
                        })) || [],
                        isActive: variant.isActive !== undefined ? variant.isActive : true
                    };
                }) || [];

                // Özellik detaylarını topla
                const allFeatureDetails: { featureName: string; featureValue: string }[] = [];
                const globalSelectedFeatures: { [key: number]: number[] } = {};

                convertedVariants.forEach(variant => {
                    variant.featureDetails?.forEach(detail => {
                        if (!globalSelectedFeatures[detail.featureDefinitionId]) {
                            globalSelectedFeatures[detail.featureDefinitionId] = [];
                        }
                        if (!globalSelectedFeatures[detail.featureDefinitionId].includes(detail.featureValueId)) {
                            globalSelectedFeatures[detail.featureDefinitionId].push(detail.featureValueId);
                        }

                        const exists = allFeatureDetails.some(
                            existing => existing.featureName === detail.featureName && existing.featureValue === detail.featureValue
                        );
                        if (!exists) {
                            allFeatureDetails.push({
                                featureName: detail.featureName,
                                featureValue: detail.featureValue,
                            });
                        }
                    });
                });

                console.log('🔄 Converted variants:', convertedVariants);
                console.log('🔄 All feature details:', allFeatureDetails);
                console.log('🔄 Global selected features:', globalSelectedFeatures);

                set({
                    formData: {
                        name: productData.name || '',
                        description: productData.description || '',
                        brandId: productData.brandId || 1,
                        categoryId: productData.categoryId || 1,
                        subCategoryId: productData.subCategoryId || 1,
                        selectedFeatures: globalSelectedFeatures,
                        price: convertedVariants[0]?.pricing.price || 0,
                        stock: convertedVariants[0]?.pricing.stock || 0,
                        extraDiscount: convertedVariants[0]?.pricing.extraDiscount || 0,
                        ratios: convertedVariants[0]?.pricing.ratios || { pvRatio: 0, cvRatio: 0, spRatio: 0 },
                        points: convertedVariants[0]?.pricing.points || { pv: 0, cv: 0, sp: 0 },
                        hasVariants: convertedVariants.length > 0, // En az 1 varyant varsa true
                        variants: [],
                        images: [], // Images varyantlarda tutuluyor
                        isActive: productData.isActive !== undefined ? productData.isActive : true,
                    },
                    selectedNames: {
                        brandName: productData.brandName || '',
                        categoryName: productData.categoryName || '',
                        subCategoryName: productData.subCategoryName || '',
                    },
                    selectedFeatureDetails: allFeatureDetails,
                    variants: convertedVariants,
                    error: null,
                });

                console.log('✅ Product initialized successfully');
                console.log('📊 Form data:', get().formData);
                console.log('🏷️ Selected names:', get().selectedNames);
                console.log('🎯 Variants:', get().variants);
                console.log('🔍 Has variants:', get().formData.hasVariants);
                console.log('🔍 Variants length:', get().variants.length);
            },

            setOriginalProductId: (id) => set({ originalProductId: id }),
        }),
        {
            name: 'edit-product-store',
            enabled: process.env.NODE_ENV === 'development',
        }
    )
);

// Selector hooks for performance
export const useEditProductFormData = () => useEditProductStore(state => state.formData);
export const useEditProductVariants = () => useEditProductStore(state => state.variants);
export const useEditProductActions = () => useEditProductStore(state => ({
    setFormData: state.setFormData,
    handleInputChange: state.handleInputChange,
    handleRatioChange: state.handleRatioChange,
    setCategorySelection: state.setCategorySelection,
    clearAllSelections: state.clearAllSelections,
    setAvailableFeatures: state.setAvailableFeatures,
    setVariants: state.setVariants,
    saveVariant: state.saveVariant,
    deleteVariant: state.deleteVariant,
    generateVariants: state.generateVariants,
    handleImageUpload: state.handleImageUpload,
    removeImage: state.removeImage,
    setMainImage: state.setMainImage,
    setError: state.setError,
    reset: state.reset,
    initializeWithProduct: state.initializeWithProduct,
    setOriginalProductId: state.setOriginalProductId,
}));