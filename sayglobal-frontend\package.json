{"name": "sayglobal-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/d3-hierarchy": "^3.1.7", "axios": "^1.10.0", "axios-retry": "^4.5.0", "browser-image-compression": "^2.0.2", "d3-hierarchy": "^3.1.2", "framer-motion": "^12.15.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}